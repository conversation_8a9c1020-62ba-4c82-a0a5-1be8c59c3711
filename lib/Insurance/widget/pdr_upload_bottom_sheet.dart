import 'package:demolition_admin_app/Insurance/model/insurance_model.dart';
import 'package:demolition_admin_app/constatnts/app_colors.dart';
import 'package:demolition_admin_app/widget/common_button.dart';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gap/gap.dart';

class PdrUploadBottomSheet extends StatefulWidget {
  const PdrUploadBottomSheet({super.key, required this.insurance});
  final InsuranceModel insurance;

  @override
  State<PdrUploadBottomSheet> createState() => _PdrUploadBottomSheetState();
}

class _PdrUploadBottomSheetState extends State<PdrUploadBottomSheet> {
  final ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);

  @override
  void dispose() {
    isLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.lightBlack,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const Gap(20),

          // Title
          Text(
            'Upload PDR Sheet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const Gap(12),

          // Description
          Text(
            'Upload PDR sheet for ${widget.insurance.value} insurance. Please select an Excel file (.xlsx) containing the PDR data.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.lightBlack,
                ),
          ),
          const Gap(24),

          // Buttons
          Row(
            children: [
              Expanded(
                child: CommonButton(
                  text: 'Cancel',
                  onTap: () => Navigator.pop(context),
                  backgroundColor: Colors.transparent,
                  showBorder: true,
                  textColor: AppColors.buttonColor,
                  removeShadow: true,
                ),
              ),
              const Gap(12),
              Expanded(
                child: ValueListenableBuilder<bool>(
                  valueListenable: isLoading,
                  builder: (context, loading, _) {
                    return CommonButton(
                      text: 'Upload',
                      isLoading: loading,
                      onTap: loading ? null : _handleUpload,
                    );
                  },
                ),
              ),
            ],
          ),

          // Add bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Future<void> _handleUpload() async {
    try {
      final pickedFile = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
        withData: true,
      );

      if (pickedFile != null && pickedFile.files.isNotEmpty) {
        isLoading.value = true;

        // Simulate processing time
        await Future.delayed(const Duration(seconds: 2));

        final bytes = pickedFile.files.single.bytes;
        if (bytes != null) {
          final excel = Excel.decodeBytes(bytes.toList());

          // Process the PDR sheet data here
          await _processPdrSheet(excel);

          isLoading.value = false;

          // Show success message
          Fluttertoast.showToast(
            msg: 'PDR sheet uploaded successfully for ${widget.insurance.value}',
          );

          // Close the bottom sheet
          if (mounted) {
            Navigator.pop(context);
          }
        }
      }
    } catch (e) {
      isLoading.value = false;
      Fluttertoast.showToast(
        msg: 'Error uploading PDR sheet: ${e.toString()}',
      );
    }
  }

  Future<void> _processPdrSheet(Excel excel) async {
    // TODO: Implement PDR sheet processing logic
    // This is where you would process the Excel data and upload it to Firebase
    // Similar to the uploadExcelCarData method in HomePage

    // For now, just simulate processing
    await Future.delayed(const Duration(milliseconds: 500));
  }

  static void show(BuildContext context, InsuranceModel insurance) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PdrUploadBottomSheet(insurance: insurance),
    );
  }
}
