import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:demolition_admin_app/Insurance/model/insurance_model.dart';
import 'package:demolition_admin_app/Insurance/model/insurance_pdr_model.dart';
import 'package:demolition_admin_app/Insurance/model/pdr_data_model.dart';
import 'package:demolition_admin_app/constatnts/app_colors.dart';
import 'package:demolition_admin_app/widget/common_button.dart';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gap/gap.dart';

class PdrUploadBottomSheet extends StatefulWidget {
  const PdrUploadBottomSheet({super.key, required this.insurance});
  final InsuranceModel insurance;

  @override
  State<PdrUploadBottomSheet> createState() => _PdrUploadBottomSheetState();
}

class _PdrUploadBottomSheetState extends State<PdrUploadBottomSheet> {
  final ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);

  @override
  void dispose() {
    isLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.lightBlack,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const Gap(20),

          // Title
          Text(
            'Upload PDR Sheet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const Gap(12),

          // Description
          Text(
            'Upload PDR sheet for ${widget.insurance.value} insurance. Please select an Excel file (.xlsx) containing the PDR pricing matrix.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.lightBlack,
                ),
          ),
          const Gap(24),

          // Buttons
          Row(
            children: [
              Expanded(
                child: CommonButton(
                  text: 'Cancel',
                  onTap: () => Navigator.pop(context),
                  backgroundColor: Colors.transparent,
                  showBorder: true,
                  textColor: AppColors.buttonColor,
                  removeShadow: true,
                ),
              ),
              const Gap(12),
              Expanded(
                child: ValueListenableBuilder<bool>(
                  valueListenable: isLoading,
                  builder: (context, loading, _) {
                    return CommonButton(
                      text: 'Upload',
                      isLoading: loading,
                      onTap: loading ? null : _handleUpload,
                    );
                  },
                ),
              ),
            ],
          ),

          // Add bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Future<void> _handleUpload() async {
    try {
      final pickedFile = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
        withData: true,
      );

      if (pickedFile != null && pickedFile.files.isNotEmpty) {
        isLoading.value = true;

        final bytes = pickedFile.files.single.bytes;
        if (bytes != null) {
          final excel = Excel.decodeBytes(bytes.toList());

          // Process the PDR sheet data
          await _processPdrSheet(excel);

          isLoading.value = false;

          // Show success message
          Fluttertoast.showToast(
            msg: 'PDR sheet uploaded successfully for ${widget.insurance.value}',
          );

          // Close the bottom sheet
          if (mounted) {
            Navigator.pop(context);
          }
        }
      }
    } catch (e) {
      isLoading.value = false;
      Fluttertoast.showToast(
        msg: 'Error uploading PDR sheet: ${e.toString()}',
      );
    }
  }

  Future<void> _processPdrSheet(Excel excel) async {
    try {
      log('Processing PDR sheet for ${widget.insurance.value}');

      final pdrDataList = <PdrData>[];

      // Process each sheet in the Excel file
      for (var table in excel.tables.keys) {
        final sheet = excel.tables[table];
        if (sheet == null) continue;

        // Skip header row (row 0) and process data rows
        for (int i = 1; i < sheet.rows.length; i++) {
          final row = sheet.rows[i];
          if (row.isEmpty) continue;

          // Extract data based on the screenshot structure:
          // Column A: PANEL, B: MIN-DENTS, C: MAX-DENTS, D: SIZE-D, E: SIZE-N, F: SIZE-Q, G: SIZE-H
          final panel = row.isNotEmpty ? row[0]?.value?.toString().trim() : null;
          final minDents = row.length > 1 ? int.tryParse(row[1]?.value?.toString() ?? '') : null;
          final maxDents = row.length > 2 ? int.tryParse(row[2]?.value?.toString() ?? '') : null;
          final sizeD = row.length > 3 ? double.tryParse(row[3]?.value?.toString() ?? '') : null;
          final sizeN = row.length > 4 ? double.tryParse(row[4]?.value?.toString() ?? '') : null;
          final sizeQ = row.length > 5 ? double.tryParse(row[5]?.value?.toString() ?? '') : null;
          final sizeH = row.length > 6 ? double.tryParse(row[6]?.value?.toString() ?? '') : null;

          // Only add rows with valid panel data
          if (panel != null && panel.isNotEmpty && panel != 'PANEL') {
            pdrDataList.add(PdrData(
              panel: panel,
              minDents: minDents,
              maxDents: maxDents,
              sizeD: sizeD,
              sizeN: sizeN,
              sizeQ: sizeQ,
              sizeH: sizeH,
            ));
          }
        }
      }

      if (pdrDataList.isNotEmpty) {
        // Create insurance PDR data object
        final insurancePdrData = InsurancePdrData(
          insuranceKey: widget.insurance.key,
          insuranceName: widget.insurance.value,
          pdrData: pdrDataList,
          uploadedAt: DateTime.now(),
        );

        // Upload to Firebase
        await _uploadPdrDataToFirebase(insurancePdrData);

        log('Successfully processed ${pdrDataList.length} PDR entries');
      } else {
        throw Exception('No valid PDR data found in the Excel file');
      }
    } catch (e) {
      log('Error processing PDR sheet: $e');
      rethrow;
    }
  }

  Future<void> _uploadPdrDataToFirebase(InsurancePdrData insurancePdrData) async {
    try {
      log('Uploading PDR data to Firebase for ${insurancePdrData.insuranceName}');

      final batch = FirebaseFirestore.instance.batch();
      final collection = FirebaseFirestore.instance.collection('pdr_data');

      // Delete existing PDR data for this insurance
      final existingDocs = await collection.where('insurance_key', isEqualTo: insurancePdrData.insuranceKey).get();

      for (final doc in existingDocs.docs) {
        batch.delete(doc.reference);
      }

      // Add new PDR data
      final docRef = collection.doc();
      batch.set(docRef, insurancePdrData.toMap());

      // Commit the batch
      await batch.commit();

      log('Successfully uploaded PDR data to Firebase');
    } catch (e) {
      log('Error uploading PDR data to Firebase: $e');
      rethrow;
    }
  }
}
