import 'package:demolition_admin_app/constatnts/app_colors.dart';
import 'package:demolition_admin_app/utility/extension/colors_extnetions.dart';
import 'package:flutter/material.dart';

class CommonContainerWidget extends StatelessWidget {
  const CommonContainerWidget({super.key, this.padding, required this.child, this.onTap, this.margin});
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Widget child;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(15),
      child: Container(
        padding: padding ?? const EdgeInsets.all(14),
        margin: margin,
        decoration: BoxDecoration(
          color: AppColors.lightBlack,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withOpacity2(0.08),
              spreadRadius: 0,
              blurRadius: 18,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: child,
      ),
    );
  }
}
