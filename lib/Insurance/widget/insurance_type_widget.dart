import 'package:demolition_admin_app/Insurance/model/insurance_model.dart';
import 'package:demolition_admin_app/Insurance/widget/common_container_widget.dart';
import 'package:demolition_admin_app/constatnts/app_assets.dart';
import 'package:demolition_admin_app/widget/app_svg_image.dart';
import 'package:flutter/material.dart';

class InsuranceTypeWidget extends StatelessWidget {
  const InsuranceTypeWidget({super.key, this.insurance, this.onTap});
  final InsuranceModel? insurance;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return CommonContainerWidget(
      onTap: onTap,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 14),
      child: Row(
        children: [
          Expanded(
            child: Text(
              insurance?.value ?? '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ),
          AppSvgImage(
            AppAssets.rightArrowIcon,
            height: 24,
            width: 24,
          ),
        ],
      ),
    );
  }
}
