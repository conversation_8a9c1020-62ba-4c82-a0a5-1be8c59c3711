import 'package:demolition_admin_app/Insurance/model/insurance_model.dart';
import 'package:demolition_admin_app/Insurance/widget/insurance_type_widget.dart';
import 'package:demolition_admin_app/Insurance/widget/pdr_upload_bottom_sheet.dart';
import 'package:demolition_admin_app/widget/common_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class InsuranceListView extends StatefulWidget {
  const InsuranceListView({super.key});

  @override
  State<InsuranceListView> createState() => _InsuranceListViewState();
}

class _InsuranceListViewState extends State<InsuranceListView> {
  final List<InsuranceModel>? insuranceList = [
    InsuranceModel(key: 'ALUMINUM_MATRIX', value: 'Aluminium'),
    InsuranceModel(key: 'AMERICAN_FAMILY_MATRIX', value: 'American Family'),
    InsuranceModel(key: 'CENTER_MUTUAL_MATRIX', value: 'Central Mutual'),
    InsuranceModel(key: 'FARMERS_MATRIX', value: 'Farmers'),
    InsuranceModel(key: 'GEICO_MATRIX', value: 'Geico'),
    InsuranceModel(key: 'LIBERTY_MUTUAL_MATRIX', value: 'Liberty Mutual'),
    InsuranceModel(key: 'NATIONWIDE_MATRIX', value: 'Nationwide'),
    InsuranceModel(key: 'PROGRESSIVE_MATRIX', value: 'Progressive'),
    InsuranceModel(key: 'STATE_FARM_MATRIX', value: 'State Farm'),
    InsuranceModel(key: 'TRAVELERS_MATRIX', value: 'Travelers'),
    InsuranceModel(key: 'USAA_MATRIX', value: 'USAA'),
    InsuranceModel(key: 'STANDARD_MATRIX', value: 'Standard'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Insurance List',
        showDivider: true,
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        itemCount: insuranceList?.length ?? 0,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          final insurance = insuranceList?[index];
          return InsuranceTypeWidget(
            insurance: insurance,
            onTap: insurance != null ? () => PdrUploadBottomSheet.show(context, insurance) : null,
          );
        },
        separatorBuilder: (context, index) => const Gap(12),
      ),
    );
  }
}
