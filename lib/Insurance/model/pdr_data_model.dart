class PdrSizePrice {
  final String? key;
  final double? price;

  PdrSizePrice({
    this.key,
    this.price,
  });

  Map<String, dynamic> toMap() {
    return {
      'key': key,
      'price': price,
    };
  }

  factory PdrSizePrice.fromMap(Map<String, dynamic> map) {
    return PdrSizePrice(
      key: map['key'],
      price: map['price']?.toDouble(),
    );
  }

  @override
  String toString() => 'PdrSizePrice(key: $key, price: $price)';
}

class PdrData {
  final String? panel;
  final int? minDents;
  final int? maxDents;
  final String? numberOfDentsRange;
  final List<PdrSizePrice> sizes;

  PdrData({
    this.panel,
    this.minDents,
    this.maxDents,
    this.numberOfDentsRange,
    this.sizes = const [],
  });

  @override
  bool operator ==(covariant PdrData other) {
    if (identical(this, other)) return true;

    return other.panel == panel &&
        other.minDents == minDents &&
        other.maxDents == maxDents &&
        other.numberOfDentsRange == numberOfDentsRange &&
        _listEquals(other.sizes, sizes);
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    if (identical(a, b)) return true;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  @override
  int get hashCode {
    return panel.hashCode ^
        minDents.hashCode ^
        maxDents.hashCode ^
        numberOfDentsRange.hashCode ^
        sizes.hashCode;
  }

  @override
  String toString() {
    return 'PdrData(panel: $panel, minDents: $minDents, maxDents: $maxDents, numberOfDentsRange: $numberOfDentsRange, sizes: $sizes)';
  }

  Map<String, dynamic> toMap() {
    return {
      'panel': panel,
      'min_dents': minDents,
      'max_dents': maxDents,
      'number_of_dents_range': numberOfDentsRange,
      'sizes': sizes.map((x) => x.toMap()).toList(),
    };
  }

  factory PdrData.fromMap(Map<String, dynamic> map) {
    return PdrData(
      panel: map['panel'],
      minDents: map['min_dents']?.toInt(),
      maxDents: map['max_dents']?.toInt(),
      numberOfDentsRange: map['number_of_dents_range'],
      sizes: List<PdrSizePrice>.from(
        map['sizes']?.map((x) => PdrSizePrice.fromMap(x)) ?? [],
      ),
    );
  }
}
