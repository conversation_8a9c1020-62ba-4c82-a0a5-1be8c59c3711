class PdrData {
  final String? panel;
  final int? minDents;
  final int? maxDents;
  final double? sizeD;
  final double? sizeN;
  final double? sizeQ;
  final double? sizeH;

  PdrData({
    this.panel,
    this.minDents,
    this.maxDents,
    this.sizeD,
    this.sizeN,
    this.sizeQ,
    this.sizeH,
  });

  @override
  bool operator ==(covariant PdrData other) {
    if (identical(this, other)) return true;

    return other.panel == panel &&
        other.minDents == minDents &&
        other.maxDents == maxDents &&
        other.sizeD == sizeD &&
        other.sizeN == sizeN &&
        other.sizeQ == sizeQ &&
        other.sizeH == sizeH;
  }

  @override
  int get hashCode {
    return panel.hashCode ^
        minDents.hashCode ^
        maxDents.hashCode ^
        sizeD.hashCode ^
        sizeN.hashCode ^
        sizeQ.hashCode ^
        sizeH.hashCode;
  }

  @override
  String toString() {
    return 'PdrData(panel: $panel, minDents: $minDents, maxDents: $maxDents, sizeD: $sizeD, sizeN: $sizeN, sizeQ: $sizeQ, sizeH: $sizeH)';
  }

  Map<String, dynamic> toMap() {
    return {
      'panel': panel,
      'min_dents': minDents,
      'max_dents': maxDents,
      'size_d': sizeD,
      'size_n': sizeN,
      'size_q': sizeQ,
      'size_h': sizeH,
    };
  }

  factory PdrData.fromMap(Map<String, dynamic> map) {
    return PdrData(
      panel: map['panel'],
      minDents: map['min_dents']?.toInt(),
      maxDents: map['max_dents']?.toInt(),
      sizeD: map['size_d']?.toDouble(),
      sizeN: map['size_n']?.toDouble(),
      sizeQ: map['size_q']?.toDouble(),
      sizeH: map['size_h']?.toDouble(),
    );
  }
}
