import 'package:demolition_admin_app/Insurance/model/pdr_data_model.dart';

class InsurancePdrData {
  final String? insuranceKey;
  final String? insuranceName;
  final List<PdrData> pdrData;
  final DateTime? uploadedAt;

  InsurancePdrData({
    this.insuranceKey,
    this.insuranceName,
    this.pdrData = const [],
    this.uploadedAt,
  });

  @override
  String toString() {
    return 'InsurancePdrData(insuranceKey: $insuranceKey, insuranceName: $insuranceName, pdrData: $pdrData, uploadedAt: $uploadedAt)';
  }

  Map<String, dynamic> toMap() {
    return {
      'insurance_key': insuranceKey,
      'insurance_name': insuranceName,
      'pdr_data': pdrData.map((x) => x.toMap()).toList(),
      'uploaded_at': uploadedAt?.millisecondsSinceEpoch,
    };
  }

  factory InsurancePdrData.fromMap(Map<String, dynamic> map) {
    return InsurancePdrData(
      insuranceKey: map['insurance_key'],
      insuranceName: map['insurance_name'],
      pdrData: List<PdrData>.from(
        map['pdr_data']?.map((x) => PdrData.fromMap(x)) ?? [],
      ),
      uploadedAt: map['uploaded_at'] != null ? DateTime.fromMillisecondsSinceEpoch(map['uploaded_at']) : null,
    );
  }
}
