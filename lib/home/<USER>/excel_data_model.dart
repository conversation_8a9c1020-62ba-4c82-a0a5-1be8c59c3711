class ExcelData {
  final String? brand;
  final int? startYear;
  final int? endYear;
  final String? model;
  final String? operations;
  final String? panel;
  final String? panelFullName;
  final String? part;
  final String? partPrice;
  final String? partNumber;
  final String? laborHours;
  final String? paintHours;
  final String? notes;

  ExcelData({
    this.brand,
    this.startYear,
    this.endYear,
    this.model,
    this.operations,
    this.panel,
    this.panelFullName,
    this.part,
    this.partPrice,
    this.partNumber,
    this.laborHours,
    this.paintHours,
    this.notes,
  });

  @override
  bool operator ==(covariant ExcelData other) {
    if (identical(this, other)) return true;

    return other.brand == brand &&
        other.startYear == startYear &&
        other.endYear == endYear &&
        other.model == model &&
        other.operations == operations &&
        other.panel == panel &&
        other.panelFullName == panelFullName &&
        other.part == part &&
        other.partPrice == partPrice &&
        other.partNumber == partNumber &&
        other.laborHours == laborHours &&
        other.paintHours == paintHours &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return brand.hashCode ^
        startYear.hashCode ^
        endYear.hashCode ^
        model.hashCode ^
        operations.hashCode ^
        panel.hashCode ^
        panelFullName.hashCode ^
        part.hashCode ^
        partPrice.hashCode ^
        partNumber.hashCode ^
        laborHours.hashCode ^
        paintHours.hashCode ^
        notes.hashCode;
  }

  @override
  String toString() {
    return 'ExcelData(brand: $brand, startYear: $startYear, endYear: $endYear, model: $model, operations: $operations, panel: $panel, panelFullName: $panelFullName, part: $part, partPrice: $partPrice, partNumber: $partNumber, laborHours: $laborHours, paintHours: $paintHours, notes: $notes)';
  }
}
