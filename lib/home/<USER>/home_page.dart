// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:demolition_admin_app/Insurance/view/insurance_list_view.dart';
import 'package:demolition_admin_app/home/<USER>/brand_wise_car_model.dart';
import 'package:demolition_admin_app/home/<USER>/excel_data_model.dart';
import 'package:demolition_admin_app/widget/common_button.dart';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final excelList = ValueNotifier<List<BrandWiseCarData>>([]);
  final isLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ValueListenableBuilder<bool>(
                valueListenable: isLoading,
                builder: (context, loading, _) {
                  return CommonButton(
                    isLoading: loading,
                    onTap: loading
                        ? null
                        : () async {
                            final pickedFile = await FilePicker.platform.pickFiles(
                              type: FileType.custom,
                              allowedExtensions: ['xlsx'],
                              withData: true,
                            );

                            // if (pickedFile != null && pickedFile.files.isNotEmpty) {}
                            if (pickedFile != null) {
                              isLoading.value = true;
                              await Future.delayed(Duration(seconds: 2));
                              var bytes = pickedFile.files.single.bytes;
                              var excel = Excel.decodeBytes(bytes!.toList());

                              uploadExcelCarData(excel);
                            }
                          },
                    text: 'Upload Vehical Data Sheet',
                  );
                }),
            SizedBox(height: 24),
            ValueListenableBuilder<bool>(
                valueListenable: isLoading,
                builder: (context, loading, _) {
                  return CommonButton(
                    isLoading: loading,
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => InsuranceListView(),
                          ));
                    },
                    text: 'Upload PDR Sheet',
                  );
                }),
            // ValueListenableBuilder<List<BradWiseCarData>>(
            //     valueListenable: excelList,
            //     builder: (context, excel, _) {
            //       return Expanded(
            //         child: excel.isEmpty
            //             ? const Center(child: Text('No data loaded'))
            //             : ListView.builder(
            //                 itemCount: excel.length,
            //                 itemBuilder: (context, index) {
            //                   return ListTile(
            //                     title: Text(
            //                       '${excel[index].toString()}\n\n',
            //                       style: Theme.of(context).textTheme.bodyMedium,
            //                     ),
            //                   );
            //                 },
            //               ),
            //       );
            //     }),
          ],
        ),
      ),
    );
  }

  Future<void> uploadExcelCarData(Excel excelData) async {
    final dataList = <BrandWiseCarData>[];

    for (var table in excelData.tables.keys) {
      final currentTableRow = <ExcelData>[];
      for (int i = 0; i < (excelData.tables[table]?.rows ?? []).length; i++) {
        if (i != 0) {
          final row = excelData.tables[table]?.rows[i];
          final yearRangeSplited = row?.firstOrNull?.value.toString().split(RegExp(r'(\s*to\s*|\s*-\s*)'));
          final startYear = int.tryParse(yearRangeSplited?.firstOrNull ?? '');
          final endYear = int.tryParse(yearRangeSplited?.lastOrNull ?? '');
          final rowData = ExcelData(
            brand: table,
            startYear: startYear,
            endYear: endYear,
            model: (row?.length ?? 0) > 1 ? (row?[1]?.value?.toString()) : null,
            operations: (row?.length ?? 0) > 2 ? (row?[2]?.value?.toString()) : null,
            panel: (row?.length ?? 0) > 3 ? (row?[3]?.value?.toString()) : null,
            panelFullName: (row?.length ?? 0) > 4 ? (row?[4]?.value?.toString()) : null,
            part: (row?.length ?? 0) > 5 ? (row?[5]?.value?.toString()) : null,
            partPrice: (row?.length ?? 0) > 6 ? (row?[6]?.value?.toString()) : null,
            partNumber: (row?.length ?? 0) > 7 ? (row?[7]?.value?.toString()) : null,
            laborHours: (row?.length ?? 0) > 8 ? (row?[8]?.value?.toString()) : null,
            paintHours: (row?.length ?? 0) > 9 ? (row?[9]?.value?.toString()) : null,
            notes: (row?.length ?? 0) > 10 ? (row?[10]?.value?.toString()) : null,
          );
          currentTableRow.add(rowData);
        }
      }
      currentTableRow.removeWhere((r) =>
          r.startYear == null ||
          r.endYear == null ||
          r.operations == null ||
          r.model == null ||
          r.panel == null ||
          r.part == null);

      while (currentTableRow.isNotEmpty) {
        final firstRow = currentTableRow.first;
        currentTableRow.removeAt(0);
        final tempList = currentTableRow.where(
            (r) => r.startYear == firstRow.startYear && r.endYear == firstRow.endYear && r.model == firstRow.model);
        dataList.add(BrandWiseCarData(
          brand: table,
          startYear: firstRow.startYear,
          endYear: firstRow.endYear,
          model: firstRow.model,
          data: [firstRow, ...tempList],
        ));
        currentTableRow.removeWhere((r) => tempList.any((e) => e == r));
      }
    }
    // excelList.value = dataList;
    await addCarData(carData: dataList);
    isLoading.value = false;
    Fluttertoast.showToast(msg: 'Data uploaded successfully');
  }

  Future<void> addCarData({required List<BrandWiseCarData> carData}) async {
    log('Start');
    final batch = FirebaseFirestore.instance.batch();
    final collection = FirebaseFirestore.instance.collection('car_data');
    final allDocs = (await collection.get()).docs;
    for (final doc in allDocs) {
      batch.delete(doc.reference);
    }
    for (var car in carData) {
      final docRef = collection.doc();

      batch.set(docRef, {
        'brand': car.brand,
        'start_year': car.startYear,
        'end_year': car.endYear,
        'model': car.model,
        'data': [
          ...car.data.map((e) => {
                'start_year': e.startYear,
                'end_year': e.endYear,
                'model': e.model,
                'operations': e.operations,
                'panel': e.panel,
                'panel_full_name': e.panelFullName,
                'part': e.part,
                'part_price': e.partPrice,
                'part_number': e.partNumber,
                'labor_hours': e.laborHours,
                'paint_hours': e.paintHours,
                'notes': e.notes,
              })
        ],
      });
    }

    await batch.commit();
  }
}
