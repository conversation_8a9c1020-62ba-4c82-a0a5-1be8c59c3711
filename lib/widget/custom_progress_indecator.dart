import 'package:demolition_admin_app/constatnts/app_colors.dart';
import 'package:flutter/material.dart';

class CustomProgressIndecator extends StatelessWidget {
  const CustomProgressIndecator({
    super.key,
    this.color,
    this.strokeWidth = 4.0,
    this.value,
  });
  final Color? color;
  final double strokeWidth;
  final double? value;

  @override
  Widget build(BuildContext context) {
    return CircularProgressIndicator(
      color: color ?? AppColors.white,
      strokeWidth: strokeWidth,
      value: value,
    );
  }
}
