import 'package:demolition_admin_app/splash/view/splash_page.dart';
import 'package:demolition_admin_app/theme/app_typography.dart';
import 'package:flutter/material.dart';

import 'constatnts/app_colors.dart';

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Demolition and Dent',
      theme: ThemeData(
          scaffoldBackgroundColor: AppColors.primary,
          textTheme: TextTheme(
            displayLarge: LightAppTypography.displayLarge,
            displayMedium: LightAppTypography.displayMedium,
            displaySmall: LightAppTypography.displaySmall,
            headlineMedium: LightAppTypography.headlineMedium,
            headlineSmall: LightAppTypography.headlineSmall,
            titleLarge: LightAppTypography.titleLarge,
            titleMedium: LightAppTypography.titleMedium,
            titleSmall: LightAppTypography.titleSmall,
            bodyLarge: LightAppTypography.bodyLarge,
            bodyMedium: LightAppTypography.bodyMedium,
            bodySmall: LightAppTypography.bodySmall,
            labelLarge: LightAppTypography.labelLarge,
            labelMedium: LightAppTypography.labelMedium,
            labelSmall: LightAppTypography.labelSmall,
          )),
      debugShowCheckedModeBanner: false,
      locale: const Locale.fromSubtags(languageCode: 'en'),
      home: const SplashPage(),
    );
  }
}
